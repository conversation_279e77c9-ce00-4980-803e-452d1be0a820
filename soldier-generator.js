
const config = require('./config');

class SoldierGenerator {
  constructor() {
    this.config = config;
  }

  // 限制兵种数量，确保不超过配置的最大值
  limitSoldierTypes(soldiers) {
    const soldierTypes = new Set();
    
    soldiers.forEach(soldier => {
      soldierTypes.add(soldier.name);
    });
    
    if (soldierTypes.size > this.config.maxSoldierTypes) {
      const allTypes = Array.from(soldierTypes);
      const selectedTypes = [];
      const typesToRemove = [];
      
      while (selectedTypes.length < this.config.maxSoldierTypes) {
        const randomIndex = Math.floor(Math.random() * allTypes.length);
        const selectedType = allTypes[randomIndex];
        if (!selectedTypes.includes(selectedType)) {
          selectedTypes.push(selectedType);
        }
      }
      
      allTypes.forEach(type => {
        if (!selectedTypes.includes(type)) {
          typesToRemove.push(type);
        }
      });
      
      console.log(`    兵种数量超过限制(${this.config.maxSoldierTypes})，随机选择兵种: ${selectedTypes.join(', ')}, 移除兵种: ${typesToRemove.join(', ')}`);
      
      const limitedSoldiers = soldiers.filter(soldier => selectedTypes.includes(soldier.name));
      
      return limitedSoldiers;
    }
    
    return soldiers;
  }

  // 生成随机士兵
  generateRandomSoldier(allowedEnemys) {
    const enemyType = allowedEnemys[Math.floor(Math.random() * allowedEnemys.length)];
    const level = Math.floor(Math.random() * (this.config.levelRange.max - this.config.levelRange.min + 1)) + this.config.levelRange.min;
    
    return {
      name: enemyType,
      level: level
    };
  }

  // 生成指定数量的随机士兵
  generateSoldiers(count, allowedEnemys) {
    const soldiers = [];
    for (let i = 0; i < count; i++) {
      const newSoldier = this.generateRandomSoldier(allowedEnemys);
      soldiers.push(newSoldier);
      // 每次生成一个士兵后尝试触发合成
      this.tryMergeAfterGenerate(soldiers);
    }
    return soldiers;
  }

  // 检查两个士兵是否可以合成（同兵种同等级，且合成后不超过最大等级）
  canMerge(soldier1, soldier2) {
    // 必须是同兵种同等级
    if (soldier1.name !== soldier2.name || soldier1.level !== soldier2.level) {
      return false;
    }

    // 合成后的等级不能超过最大等级
    const mergedLevel = soldier1.level + 1;
    return mergedLevel <= this.config.maxSoldierLv;
  }

  // 合成两个士兵
  mergeSoldiers(soldier1, soldier2) {
    if (!this.canMerge(soldier1, soldier2)) {
      throw new Error('无法合成不同兵种或不同等级的士兵，或合成后会超过最大等级');
    }

    const mergedLevel = soldier1.level + 1;

    // 确保不超过最大等级（双重保险）
    if (mergedLevel > this.config.maxSoldierLv) {
      throw new Error(`合成后等级${mergedLevel}超过最大等级${this.config.maxSoldierLv}`);
    }

    return {
      name: soldier1.name,
      level: mergedLevel
    };
  }

  // 完全合成所有可能的士兵组合
  performCompleteRemove(soldiers) {
    let hasChanges = true;
    
    while (hasChanges) {
      hasChanges = false;
      
      // 按兵种和等级分组
      const groups = {};
      soldiers.forEach((soldier, index) => {
        const key = `${soldier.name}_${soldier.level}`;
        if (!groups[key]) {
          groups[key] = [];
        }
        groups[key].push(index);
      });

      // 找到可以合成的分组（至少有两个士兵）
      const mergeableGroups = Object.entries(groups).filter(([key, indices]) => indices.length >= 2);
      
      if (mergeableGroups.length > 0) {
        // 随机选择一个可合成的分组
        const randomGroupIndex = Math.floor(Math.random() * mergeableGroups.length);
        const [key, indices] = mergeableGroups[randomGroupIndex];

        // 选择前两个士兵进行合成
        const index1 = indices[0];
        const index2 = indices[1];
        
        const mergedSoldier = this.mergeSoldiers(soldiers[index1], soldiers[index2]);

        // 移除被合成的士兵（从后往前删除，避免索引变化）
        soldiers.splice(Math.max(index1, index2), 1);
        soldiers.splice(Math.min(index1, index2), 1);
        soldiers.push(mergedSoldier);
        
        hasChanges = true;
      }
    }
    
    return soldiers;
  }

  // 在生成一个士兵后尝试合成
  tryMergeAfterGenerate(soldiers) {
    // 每次生成士兵后以配置的概率触发合成
    if (Math.random() >= this.config.mergeProbability) {
      return soldiers;
    }

    // 如果触发合成，执行一轮完整的合成
    this.performSingleMergeRound(soldiers);
    
    // 然后尝试再次合成
    this.tryReMergeAll(soldiers);

    return soldiers;
  }

  // 执行一轮合成（只合成一对士兵）
  performSingleMergeRound(soldiers) {
    // 按兵种和等级分组
    const groups = {};
    soldiers.forEach((soldier, index) => {
      const key = `${soldier.name}_${soldier.level}`;
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(index);
    });

    // 找到可以合成的分组（至少有两个士兵）
    const mergeableGroups = Object.entries(groups).filter(([key, indices]) => indices.length >= 2);
    if (mergeableGroups.length === 0) {
      return soldiers;
    }

    // 随机选择一个可合成的分组
    const randomGroupIndex = Math.floor(Math.random() * mergeableGroups.length);
    const [key, indices] = mergeableGroups[randomGroupIndex];

    // 随机选择两个士兵进行合成
    const randomIndex1 = indices[Math.floor(Math.random() * indices.length)];
    let randomIndex2 = indices[Math.floor(Math.random() * (indices.length - 1))];
    if (randomIndex2 >= randomIndex1) randomIndex2 = indices[randomIndex2 + 1] || indices[0];

    const mergedSoldier = this.mergeSoldiers(soldiers[randomIndex1], soldiers[randomIndex2]);

    // 移除被合成的士兵，添加新士兵
    soldiers.splice(Math.max(randomIndex1, randomIndex2), 1);
    soldiers.splice(Math.min(randomIndex1, randomIndex2), 1);
    soldiers.push(mergedSoldier);

    return soldiers;
  }

  // 尝试对所有士兵进行再次合成
  tryReMergeAll(soldiers) {
    // 以配置的概率尝试再次合成
    while (Math.random() < this.config.remergeProbability) {
      // 按兵种和等级分组
      const groups = {};
      soldiers.forEach((soldier, index) => {
        const key = `${soldier.name}_${soldier.level}`;
        if (!groups[key]) {
          groups[key] = [];
        }
        groups[key].push(index);
      });

      // 找到可以合成的分组（至少有两个士兵）
      const mergeableGroups = Object.entries(groups).filter(([key, indices]) => indices.length >= 2);
      if (mergeableGroups.length === 0) {
        break; // 没有可合成的士兵
      }

      // 随机选择一个可合成的分组
      const randomGroupIndex = Math.floor(Math.random() * mergeableGroups.length);
      const [key, indices] = mergeableGroups[randomGroupIndex];

      // 选择前两个士兵进行合成
      const index1 = indices[0];
      const index2 = indices[1];
      
      const mergedSoldier = this.mergeSoldiers(soldiers[index1], soldiers[index2]);

      // 移除被合成的士兵，添加新士兵
      soldiers.splice(Math.max(index1, index2), 1);
      soldiers.splice(Math.min(index1, index2), 1);
      soldiers.push(mergedSoldier);
    }
  }

  // 生成一波士兵（包含合成逻辑）
  generateWave(soldierCount, allowedEnemys) {
    let soldiers = this.generateSoldiers(soldierCount, allowedEnemys);
    soldiers = this.limitSoldierTypes(soldiers);
    return soldiers;
  }

  // 添加士兵并触发合成逻辑
  addSoldierWithMerge(soldiers, newSoldier) {
    // 添加新士兵到数组
    soldiers.push(newSoldier);

    // 触发合成逻辑
    this.tryMergeAfterGenerate(soldiers);

    return soldiers;
  }

  // 生成累积波次士兵（保留上一波士兵 + 新增士兵）
  generateCumulativeWave(previousSoldiers, newSoldierCount, allowedEnemys) {
    let currentSoldiers = [...previousSoldiers];

    // 逐个添加新士兵，每个都触发合成逻辑
    for (let i = 0; i < newSoldierCount; i++) {
      const newSoldier = this.generateRandomSoldier(allowedEnemys);
      this.addSoldierWithMerge(currentSoldiers, newSoldier);
    }

    return currentSoldiers;
  }

  // 统计士兵数量
  countSoldiers(soldiers) {
    const counts = {};
    soldiers.forEach(soldier => {
      const key = `${soldier.name}_${soldier.level}`;
      counts[key] = (counts[key] || 0) + 1;
    });
    return counts;
  }

  // 格式化士兵统计为CSV格式
  formatSoldierCounts(counts) {
    return Object.entries(counts)
      .map(([key, count]) => `${key}_${count}`)
      .join(', ');
  }

  // 格式化士兵统计为数组格式 [["士兵名", 等级, 数量], ...]
  formatSoldierArray(counts) {
    return Object.entries(counts).map(([key, count]) => {
      const [name, level] = key.split('_');
      return [name, parseInt(level), count];
    });
  }
}

module.exports = SoldierGenerator;
