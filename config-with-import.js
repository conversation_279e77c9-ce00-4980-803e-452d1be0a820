// 游戏配置 - 导入外部波次配置示例
const config = {
  // 等级配置
  levelRange: {
    min: 1,
    max: 3
  },
  
  // 合成概率 (30%)
  mergeProbability: 0.3,
  
  // 每波士兵数量范围
  soldiersPerWave: {
    min: 10,
    max: 10
  },

  // 输出文件路径
  outputPath: 'enemy-soldiers.json',

  // 导入外部波次配置
  // 方式1: 直接导入JSON文件
  // waveConfig: require('./wave-config.json'),

  // 方式2: 导入JS模块
  // waveConfig: require('./wave-config-module.js'),

  // 方式3: 动态加载（在index.js中处理）
  // 不设置waveConfig，程序会自动从wave-config.json加载
};

module.exports = config; 