// 游戏配置
const config = {
  // 等级配置
  levelRange: {
    min: 1,
    max: 3
  },
  
  // 合成概率 (30%)
  mergeProbability: 0.5,
  remergeProbability: 0.5,
  
  // 每波士兵数量范围
  soldiersPerWave: {
    min: 20,
    max: 20
  },

  maxSoldierLv:8,

  // 最多兵种数量（上阵的兵种种类最多是这个值）
  maxSoldierTypes: 4,

  // 输出文件路径
  outputPath: '../enemy-soldiers.json',

  // 波次配置 - 可以直接在这里配置，也可以导入外部文件
  waveConfig: require('../../~out/design/~selftest/stage.js')
};

module.exports = config; 