const SoldierGenerator = require('./soldier-generator');

// 测试士兵生成器
function testSoldierGenerator() {
  console.log('🧪 开始测试士兵生成器...\n');
  
  const generator = new SoldierGenerator();
  
  // 测试用的敌人类型
  const testEnemys = ["枪兵", "剑士", "弓箭手"];
  
  // 测试1: 生成单个随机士兵
  console.log('测试1: 生成单个随机士兵');
  const soldier = generator.generateRandomSoldier(testEnemys);
  console.log(`生成的士兵: ${soldier.name}_${soldier.level} (攻击:${soldier.attack}, 防御:${soldier.defense})`);
  
  // 测试2: 生成多个士兵
  console.log('\n测试2: 生成5个随机士兵');
  const soldiers = generator.generateSoldiers(5, testEnemys);
  soldiers.forEach((s, i) => {
    console.log(`  士兵${i+1}: ${s.name}_${s.level}`);
  });
  
  // 测试3: 测试合成逻辑
  console.log('\n测试3: 测试合成逻辑');
  const testSoldiers = [
    { name: '枪兵', level: 1, attack: 10, defense: 5 },
    { name: '枪兵', level: 1, attack: 10, defense: 5 },
    { name: '剑士', level: 1, attack: 8, defense: 8 },
    { name: '剑士', level: 1, attack: 8, defense: 8 },
    { name: '弓箭手', level: 2, attack: 24, defense: 6 }
  ];
  
  console.log('原始士兵:');
  testSoldiers.forEach((s, i) => {
    console.log(`  ${i+1}. ${s.name}_${s.level}`);
  });
  
  const mergedSoldiers = generator.tryMergeSoldiers(testSoldiers);
  console.log('\n合成后士兵:');
  mergedSoldiers.forEach((s, i) => {
    console.log(`  ${i+1}. ${s.name}_${s.level} (攻击:${s.attack}, 防御:${s.defense})`);
  });
  
  // 测试4: 统计士兵数量
  console.log('\n测试4: 统计士兵数量');
  const counts = generator.countSoldiers(mergedSoldiers);
  console.log('士兵统计:', counts);
  
  // 测试5: 格式化输出
  console.log('\n测试5: 格式化输出');
  const formatted = generator.formatSoldierCounts(counts);
  console.log('格式化结果:', formatted);
  
  // 测试6: 测试累积波次生成
  console.log('\n测试6: 测试累积波次生成');
  const wave1Soldiers = generator.generateWave(6, testEnemys);
  console.log(`第1波士兵数: ${wave1Soldiers.length}`);
  
  const wave2Soldiers = generator.generateCumulativeWave(wave1Soldiers, 4, testEnemys);
  console.log(`第2波士兵数: ${wave2Soldiers.length}`);
  
  console.log('\n✅ 测试完成！');
}

// 运行测试
if (require.main === module) {
  testSoldierGenerator();
}

module.exports = { testSoldierGenerator }; 