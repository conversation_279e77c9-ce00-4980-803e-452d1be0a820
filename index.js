const fs = require('fs');
const path = require('path');
const SoldierGenerator = require('./soldier-generator');
const config = require('./config');

class EnemyGenerator {
  constructor() {
    this.soldierGenerator = new SoldierGenerator();
    this.config = config;
    this.waveConfig = this.loadWaveConfig();
  }

  // 加载波次配置
  loadWaveConfig() {
    // 如果config中有waveConfig，直接使用
    if (this.config.waveConfig) {
      return this.config.waveConfig;
    }

    // 否则尝试从外部文件加载
    try {
      const configPath = path.join(__dirname, 'wave-config.json');
      if (fs.existsSync(configPath)) {
        const configData = fs.readFileSync(configPath, 'utf8');
        return JSON.parse(configData);
      } else {
        throw new Error('未找到波次配置文件');
      }
    } catch (error) {
      console.error('加载波次配置失败:', error.message);
      process.exit(1);
    }
  }

  // 将字典格式转换为数组格式
  convertWaveConfigToArray(waveConfig) {
    if (Array.isArray(waveConfig)) {
      return waveConfig; // 已经是数组格式
    }

    // 如果是字典格式，转换为数组
    const levels = [];
    Object.keys(waveConfig).forEach(levelKey => {
      const levelData = waveConfig[levelKey];
      levels.push({
        level: parseInt(levelKey),
        ...levelData
      });
    });

    // 按关卡数字排序
    levels.sort((a, b) => a.level - b.level);
    
    return { levels };
  }

  // 应用关卡级别的兵种数量限制
  applyLevelSoldierTypeLimit(soldiers, levelSoldierTypes) {
    // 如果关卡兵种类型已经确定（数量已达到限制），则只允许使用已确定的兵种
    if (levelSoldierTypes.size >= this.soldierGenerator.config.maxSoldierTypes) {
      const allowedTypes = Array.from(levelSoldierTypes);
      const filteredSoldiers = soldiers.filter(soldier => allowedTypes.includes(soldier.name));
      
      if (filteredSoldiers.length !== soldiers.length) {
        const removedTypes = new Set();
        soldiers.forEach(soldier => {
          if (!allowedTypes.includes(soldier.name)) {
            removedTypes.add(soldier.name);
          }
        });
        console.log(`    关卡兵种已确定(${allowedTypes.join(', ')})，移除新兵种: ${Array.from(removedTypes).join(', ')}`);
      }
      
      return filteredSoldiers;
    }
    
    // 统计当前士兵中的兵种类型
    const currentSoldierTypes = new Set();
    soldiers.forEach(soldier => {
      currentSoldierTypes.add(soldier.name);
    });
    
    // 合并到关卡级别的兵种类型集合中
    currentSoldierTypes.forEach(type => {
      levelSoldierTypes.add(type);
    });
    
    // 如果关卡级别的兵种数量超过限制，需要随机选择兵种
    if (levelSoldierTypes.size > this.soldierGenerator.config.maxSoldierTypes) {
      const allTypes = Array.from(levelSoldierTypes);
      const selectedTypes = [];
      const typesToRemove = [];
      
      // 随机选择maxSoldierTypes个兵种
      while (selectedTypes.length < this.soldierGenerator.config.maxSoldierTypes) {
        const randomIndex = Math.floor(Math.random() * allTypes.length);
        const selectedType = allTypes[randomIndex];
        if (!selectedTypes.includes(selectedType)) {
          selectedTypes.push(selectedType);
        }
      }
      
      // 找出被移除的兵种
      allTypes.forEach(type => {
        if (!selectedTypes.includes(type)) {
          typesToRemove.push(type);
        }
      });
      
      console.log(`    关卡兵种数量超过限制(${this.soldierGenerator.config.maxSoldierTypes})，随机选择兵种: ${selectedTypes.join(', ')}, 移除兵种: ${typesToRemove.join(', ')}`);
      
      // 清空关卡兵种类型集合，重新设置为选中的兵种
      levelSoldierTypes.clear();
      selectedTypes.forEach(type => {
        levelSoldierTypes.add(type);
      });
      
      // 只保留随机选择的兵种的士兵
      const limitedSoldiers = soldiers.filter(soldier => selectedTypes.includes(soldier.name));
      
      return limitedSoldiers;
    }
    
    return soldiers;
  }

  // 生成单个关卡的士兵数据
  generateLevel(levelConfig) {
    const level = levelConfig.level;
    const firstSoldierCount = levelConfig.firstSoldierCount;
    const waveCount = levelConfig.waveCount;
    const allowedEnemys = levelConfig.Enemys;
    const levelData = {
      level: level,
      waves: []
    };

    console.log(`\n正在生成第 ${level} 关的士兵数据...`);
    console.log(`  第1波初始士兵数: ${firstSoldierCount}, 总波次: ${waveCount}`);
    console.log(`  允许的敌人类型: ${allowedEnemys.join(', ')}`);

    let previousSoldiers = []; // 上一波的士兵
    let levelSoldierTypes = new Set(); // 关卡级别的兵种类型集合

    // 根据waveCount生成波次
    for (let waveIndex = 0; waveIndex < waveCount; waveIndex++) {
      const wave = waveIndex + 1;
      const newSoldierCount = this.soldierGenerator.config.soldiersPerWave.min; // 每波新增的士兵数

      console.log(`  生成第 ${wave} 波...`);

      let soldiers;
      if (waveIndex === 0) {
        // 第1波：使用关卡配置的firstSoldierCount
        console.log(`    基础士兵数: ${firstSoldierCount}`);
        soldiers = this.soldierGenerator.generateWave(firstSoldierCount, allowedEnemys);
      } else {
        // 第2波及以后：上一波士兵 + 新增士兵
        console.log(`    上一波士兵数: ${previousSoldiers.length}, 新增士兵数: ${newSoldierCount}`);
        soldiers = this.soldierGenerator.generateCumulativeWave(previousSoldiers, newSoldierCount, allowedEnemys);
      }
      
      // 应用关卡级别的兵种数量限制
      soldiers = this.applyLevelSoldierTypeLimit(soldiers, levelSoldierTypes);
      
      // 统计士兵数量
      const soldierCounts = this.soldierGenerator.countSoldiers(soldiers);
      
      // 转换为数组格式
      const soldierArray = this.soldierGenerator.formatSoldierArray(soldierCounts);
      
      levelData.waves.push({
        wave: wave,
        firstSoldierCount: firstSoldierCount,
        newSoldierCount: newSoldierCount,
        actualSoldiers: soldiers.length,
        soldiers: soldiers,
        soldierArray: soldierArray
      });

      console.log(`    实际士兵数: ${soldiers.length}, 士兵组成: ${JSON.stringify(soldierArray)}`);

      // 保存这一波的士兵，供下一波使用
      previousSoldiers = [...soldiers];
    }

    return levelData;
  }

  // 生成所有关卡数据
  generateAllLevels() {
    const allLevels = [];
    
    // 转换配置格式
    const waveConfigArray = this.convertWaveConfigToArray(this.waveConfig);
    
    waveConfigArray.levels.forEach(levelConfig => {
      const levelData = this.generateLevel(levelConfig);
      allLevels.push(levelData);
    });

    return allLevels;
  }

  // 输出到JSON文件
  async outputToJSON(levelsData) {
    try {
      const jsonPath = this.config.outputPath;
      let jsonContent = '{\n';
      
      const lines = [];
      levelsData.forEach(levelData => {
        levelData.waves.forEach(waveData => {
          const key = `${levelData.level}_${waveData.wave}`;
          const soldierArray = JSON.stringify(waveData.soldierArray);
          lines.push(`  "${key}":${soldierArray}`);
        });
      });
      
      jsonContent += lines.join(',\n');
      jsonContent += '\n}';
      
      fs.writeFileSync(jsonPath, jsonContent, 'utf8');
      console.log(`\n✅ 数据已成功输出到 ${jsonPath}`);
    } catch (error) {
      console.error('❌ 输出JSON文件失败:', error.message);
    }
  }

  // 输出到控制台（格式化显示）
  outputToConsole(levelsData) {
    console.log('\n📊 生成的敌人士兵数据:');
    console.log('=' * 50);
    
    levelsData.forEach(levelData => {
      console.log(`\n第 ${levelData.level} 关:`);
      levelData.waves.forEach(waveData => {
        console.log(`  第 ${waveData.wave} 波: ${JSON.stringify(waveData.soldierArray)}`);
      });
    });
  }

  // 运行生成器
  async run() {
    console.log('🎮 敌人士兵生成器启动');
    console.log('配置信息:');
    console.log(`- 等级范围: ${this.soldierGenerator.config.levelRange.min}-${this.soldierGenerator.config.levelRange.max}`);
    console.log(`- 合成概率: ${this.soldierGenerator.config.mergeProbability * 100}%`);
    console.log(`- 每波新增士兵数: ${this.soldierGenerator.config.soldiersPerWave.min}`);
    console.log(`- 每关最多兵种数量: ${this.soldierGenerator.config.maxSoldierTypes}`);
    console.log(`- 输出文件: ${this.config.outputPath}`);
    
    // 转换配置格式并显示关卡数量
    const waveConfigArray = this.convertWaveConfigToArray(this.waveConfig);
    console.log(`- 关卡数量: ${waveConfigArray.levels.length}`);

    // 生成所有关卡数据
    const levelsData = this.generateAllLevels();

    // 输出到控制台
    this.outputToConsole(levelsData);

    // 输出到JSON文件
    await this.outputToJSON(levelsData);

    console.log('\n🎉 生成完成！');
  }
}

// 运行程序
if (require.main === module) {
  const generator = new EnemyGenerator();
  generator.run().catch(error => {
    console.error('程序运行失败:', error.message);
    process.exit(1);
  });
}

module.exports = EnemyGenerator; 