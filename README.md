# 敌人士兵生成器

一个Node.js程序，用于根据关卡波次表生成敌人士兵，支持随机生成士兵、2合1合成逻辑，并输出JSON格式数据。

## 功能特性

- 🎯 **随机士兵生成**: 根据配置的敌人类型随机生成士兵
- 🔄 **2合1合成逻辑**: 同兵种同等级士兵有30%概率合成更高等级
- 📊 **波次管理**: 支持多关卡多波次配置
- 📈 **累积机制**: 每波士兵为上一波合成后士兵加新增士兵的累积
- 🎮 **灵活配置**: 支持字典格式和数组格式的波次配置
- 📄 **JSON输出**: 输出格式化的JSON数据

## 安装和运行

```bash
# 安装依赖
npm install

# 运行程序
node index.js
```

## 配置说明

### 主配置文件 (config.js)

```javascript
const config = {
  // 等级范围
  levelRange: {
    min: 1,
    max: 3
  },
  
  // 合成概率 (30%)
  mergeProbability: 0.3,
  
  // 每波新增士兵数量
  soldiersPerWave: {
    min: 10,
    max: 10
  },

  // 输出文件路径
  outputPath: 'enemy-soldiers.json',

  // 波次配置 - 字典格式，以关卡数字为键
  waveConfig: {
    "1": {
      firstSoldierCount: 8,
      waveCount: 3,
      Enemys: ["枪兵", "剑士", "弓箭手"]
    },
    "2": {
      firstSoldierCount: 10,
      waveCount: 4,
      Enemys: ["枪兵", "剑士", "弓箭手", "法师"]
    },
    "3": {
      firstSoldierCount: 12,
      waveCount: 5,
      Enemys: ["枪兵", "剑士", "弓箭手", "法师", "坦克"]
    }
  }
};
```

### 配置导入方式

程序支持多种配置导入方式：

1. **直接在config.js中配置** (推荐)
2. **导入外部JSON文件**:
   ```javascript
   waveConfig: require('./wave-config.json')
   ```
3. **导入外部JS模块**:
   ```javascript
   waveConfig: require('./wave-config-module.js')
   ```

### 波次配置格式

#### 字典格式 (推荐)
```javascript
{
  "1": {
    firstSoldierCount: 8,
    waveCount: 3,
    Enemys: ["枪兵", "剑士", "弓箭手"]
  },
  "2": {
    firstSoldierCount: 10,
    waveCount: 4,
    Enemys: ["枪兵", "剑士", "弓箭手", "法师"]
  }
}
```

#### 数组格式 (兼容)
```javascript
{
  levels: [
    {
      level: 1,
      firstSoldierCount: 8,
      waveCount: 3,
      Enemys: ["枪兵", "剑士", "弓箭手"]
    },
    {
      level: 2,
      firstSoldierCount: 10,
      waveCount: 4,
      Enemys: ["枪兵", "剑士", "弓箭手", "法师"]
    }
  ]
}
```

## 波次逻辑

1. **第1波**: 使用关卡配置的 `firstSoldierCount` 生成初始士兵
2. **后续波次**: 上一波合成后的士兵 + 新增士兵（由 `soldiersPerWave.min` 指定）
3. **合成机制**: 同兵种同等级士兵有30%概率合成更高等级
4. **累积效果**: 每波士兵数量会随着合成和新增而累积增长

## 输出格式

程序输出JSON格式数据，结构如下：

```json
{
  "1_1":[["枪兵",1,3],["剑士",1,2],["弓箭手",1,3]],
  "1_2":[["枪兵",1,2],["枪兵",2,1],["剑士",1,1],["剑士",2,1],["弓箭手",1,2],["弓箭手",2,1],["法师",1,3]],
  "1_3":[["枪兵",1,1],["枪兵",2,2],["枪兵",3,1],["剑士",1,1],["剑士",2,1],["弓箭手",1,1],["弓箭手",2,1],["法师",1,2],["法师",2,1],["坦克",1,2]],
  "2_1":[["枪兵",1,4],["剑士",1,3],["弓箭手",1,3]],
  "2_2":[["枪兵",1,3],["枪兵",2,1],["剑士",1,2],["剑士",2,1],["弓箭手",1,2],["弓箭手",2,1],["法师",1,2],["法师",2,1],["坦克",1,1]]
}
```

格式说明：
- 键格式: `"关卡_波次"` (如 "1_1" 表示第1关第1波)
- 值格式: `[["士兵名",等级,数量],...]`
- 每个关卡波次占一行，行内不换行

## 士兵属性

士兵的攻击力和防御力通过内部映射函数计算：

- **枪兵**: 攻击力=等级×10, 防御力=等级×5
- **剑士**: 攻击力=等级×8, 防御力=等级×8  
- **弓箭手**: 攻击力=等级×12, 防御力=等级×3
- **法师**: 攻击力=等级×15, 防御力=等级×2
- **坦克**: 攻击力=等级×5, 防御力=等级×15

## 示例配置

### 字典格式配置 (wave-config-dict.js)
```javascript
const waveConfig = {
  "1": {
    firstSoldierCount: 8,
    waveCount: 3,
    Enemys: ["枪兵", "剑士", "弓箭手"]
  },
  "2": {
    firstSoldierCount: 10,
    waveCount: 4,
    Enemys: ["枪兵", "剑士", "弓箭手", "法师"]
  }
};

module.exports = waveConfig;
```

### 外部JSON配置 (wave-config.json)
```json
{
  "1": {
    "firstSoldierCount": 8,
    "waveCount": 3,
    "Enemys": ["枪兵", "剑士", "弓箭手"]
  },
  "2": {
    "firstSoldierCount": 10,
    "waveCount": 4,
    "Enemys": ["枪兵", "剑士", "弓箭手", "法师"]
  }
}
```

## 程序结构

```
tools/
├── index.js              # 主程序入口
├── config.js             # 主配置文件
├── soldier-generator.js  # 士兵生成器
├── wave-config-dict.js   # 字典格式配置示例
├── wave-config.json      # JSON格式配置示例
├── wave-config-module.js # 模块格式配置示例
└── README.md            # 说明文档
```

## 使用说明

1. 根据需要修改 `config.js` 中的配置
2. 运行 `node index.js` 生成敌人士兵数据
3. 查看控制台输出的详细信息
4. 检查生成的JSON文件

程序会自动处理字典格式和数组格式的配置，确保兼容性和灵活性。 